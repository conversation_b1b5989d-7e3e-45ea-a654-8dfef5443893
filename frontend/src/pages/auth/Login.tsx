import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/AuthContext';
import { Link as RouterLink } from 'react-router-dom';
import { getErrorMessage } from '../../utils/errorUtils';
import {
  Box, TextField, Button, Typography, CircularProgress, Alert, Link, InputAdornment, IconButton
} from '@mui/material';
import { Visibility, VisibilityOff, Person, Lock, TrendingUp } from '@mui/icons-material';

const Login: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { login, user, loading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (user) {
      // Redirect based on role
      switch (user.role) {
        case 'admin':
          navigate('/admin/dashboard');
          break;
        case 'staff':
          navigate('/staff/dashboard');
          break;
        case 'client':
          navigate('/client/dashboard');
          break;
        default:
          navigate('/');
      }
    }
  }, [user, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    try {
      await login({ username, password });
    } catch (err: any) {
      setError(getErrorMessage(err));
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        width: '100%',
        display: 'flex',
        overflow: 'hidden',
        position: 'relative'
      }}
    >
      {/* Desktop Left Side - Branding */}
      <Box
        sx={{
          width: { md: '60%' },
          background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%)',
          display: { xs: 'none', md: 'flex' },
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          color: 'white',
          position: 'relative',
          padding: 4,
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `
              radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%)
            `,
            opacity: 0.8
          }
        }}
      >
        <Box sx={{ textAlign: 'center', zIndex: 1, maxWidth: 600 }}>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              margin: '0 auto 3rem',
            }}
          >
            <img
              src="/ampersandlogo.svg"
              alt="Ampersand Capital Logo"
              style={{
                height: '120px',
                width: 'auto',
                marginBottom: '1.5rem',
                filter: 'drop-shadow(0 25px 50px rgba(59, 130, 246, 0.4))'
              }}
            />
            <Typography variant="h1" sx={{ fontWeight: 700, mb: 3, fontSize: '4rem' }}>
              Ampersand Capital
            </Typography>
          </Box>

          <Typography variant="h4" sx={{ opacity: 0.9, mb: 4, fontWeight: 300 }}>
            Professional Investment Management
          </Typography>

          <Typography variant="h6" sx={{ opacity: 0.8, lineHeight: 1.8, maxWidth: 500, margin: '0 auto' }}>
            Secure access to your investment portfolio, performance analytics, and comprehensive financial reporting.
            Your trusted partner in wealth management.
          </Typography>
        </Box>
      </Box>

      {/* Desktop Right Side - Login Form */}
      <Box
        sx={{
          width: { md: '40%' },
          display: { xs: 'none', md: 'flex' },
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#ffffff',
          padding: 6
        }}
      >
        <Box sx={{ width: '100%', maxWidth: 450 }}>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              width: '100%'
            }}
          >
            {/* Login Header */}
            <Typography
              variant="h3"
              component="h1"
              sx={{
                fontWeight: 700,
                color: '#0f172a',
                mb: 2,
                textAlign: 'center'
              }}
            >
              Welcome Back
            </Typography>

            <Typography
              variant="h6"
              color="text.secondary"
              sx={{ mb: 6, textAlign: 'center', fontWeight: 400 }}
            >
              Please sign in to access your account
            </Typography>

            <Box component="form" onSubmit={handleSubmit} noValidate sx={{ width: '100%' }}>
              <TextField
                margin="normal"
                required
                fullWidth
                id="username"
                label="Username"
                name="username"
                autoComplete="username"
                autoFocus
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                slotProps={{
                  input: {
                    startAdornment: (
                      <InputAdornment position="start">
                        <Person sx={{ color: '#64748b', fontSize: 24 }} />
                      </InputAdornment>
                    ),
                  }
                }}
                sx={{
                  mb: 3,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    height: 60,
                    fontSize: '1.1rem',
                    backgroundColor: '#f8fafc',
                    border: '2px solid transparent',
                    '& fieldset': {
                      borderColor: '#e2e8f0',
                    },
                    '&:hover': {
                      backgroundColor: '#f1f5f9',
                      '& fieldset': {
                        borderColor: '#3b82f6',
                      },
                    },
                    '&.Mui-focused': {
                      backgroundColor: 'white',
                      '& fieldset': {
                        borderColor: '#3b82f6',
                        borderWidth: '2px',
                      },
                    },
                  },
                  '& .MuiInputLabel-root': {
                    fontSize: '1.1rem',
                    '&.Mui-focused': {
                      color: '#3b82f6',
                    },
                  },
                }}
              />
              <TextField
                margin="normal"
                required
                fullWidth
                name="password"
                label="Password"
                type={showPassword ? 'text' : 'password'}
                id="password"
                autoComplete="current-password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                slotProps={{
                  input: {
                    startAdornment: (
                      <InputAdornment position="start">
                        <Lock sx={{ color: '#64748b', fontSize: 24 }} />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={() => setShowPassword(!showPassword)}
                          edge="end"
                          sx={{ color: '#64748b' }}
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }
                }}
                sx={{
                  mb: 4,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    height: 60,
                    fontSize: '1.1rem',
                    backgroundColor: '#f8fafc',
                    border: '2px solid transparent',
                    '& fieldset': {
                      borderColor: '#e2e8f0',
                    },
                    '&:hover': {
                      backgroundColor: '#f1f5f9',
                      '& fieldset': {
                        borderColor: '#3b82f6',
                      },
                    },
                    '&.Mui-focused': {
                      backgroundColor: 'white',
                      '& fieldset': {
                        borderColor: '#3b82f6',
                        borderWidth: '2px',
                      },
                    },
                  },
                  '& .MuiInputLabel-root': {
                    fontSize: '1.1rem',
                    '&.Mui-focused': {
                      color: '#3b82f6',
                    },
                  },
                }}
              />

              {error && (
                <Alert
                  severity="error"
                  sx={{
                    width: '100%',
                    mb: 3,
                    borderRadius: 2,
                    backgroundColor: '#fef2f2',
                    border: '1px solid #fecaca',
                    color: '#dc2626'
                  }}
                >
                  {error}
                </Alert>
              )}

              <Button
                type="submit"
                fullWidth
                variant="contained"
                disabled={loading}
                sx={{
                  height: 60,
                  borderRadius: 3,
                  background: 'linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%)',
                  fontSize: '1.2rem',
                  fontWeight: 600,
                  textTransform: 'none',
                  boxShadow: '0 8px 32px rgba(30, 64, 175, 0.3)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%)',
                    boxShadow: '0 12px 40px rgba(30, 64, 175, 0.4)',
                    transform: 'translateY(-2px)',
                  },
                  '&:disabled': {
                    background: '#e2e8f0',
                    color: '#64748b',
                    boxShadow: 'none',
                    transform: 'none',
                  },
                }}
              >
                {loading ? (
                  <CircularProgress size={24} sx={{ color: 'white' }} />
                ) : (
                  'Sign In'
                )}
              </Button>

              <Box sx={{ textAlign: 'center', mt: 4 }}>
                <Link
                  component={RouterLink}
                  to="/password-reset"
                  variant="body2"
                  sx={{
                    color: '#64748b',
                    textDecoration: 'none',
                    fontSize: '1rem',
                    '&:hover': {
                      color: '#3b82f6',
                      textDecoration: 'underline'
                    }
                  }}
                >
                  Forgot password?
                </Link>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>

      {/* Mobile View */}
      <Box
        sx={{
          width: '100%',
          minHeight: '100vh',
          display: { xs: 'flex', md: 'none' },
          flexDirection: 'column',
          background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%)',
          position: 'relative',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `
              radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%)
            `,
            opacity: 0.8
          }
        }}
      >
        {/* Mobile Header */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            textAlign: 'center',
            padding: { xs: 2, sm: 3 },
            paddingTop: { xs: 4, sm: 6 },
            paddingBottom: { xs: 2, sm: 3 },
            zIndex: 1
          }}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              margin: '0 auto 1.5rem',
            }}
          >
            <img
              src="/ampersandlogo.svg"
              alt="Ampersand Capital Logo"
              style={{
                height: '70px',
                width: 'auto',
                marginBottom: '1rem',
                filter: 'drop-shadow(0 15px 30px rgba(59, 130, 246, 0.3))'
              }}
            />
            <Typography
              variant="h5"
              sx={{
                fontWeight: 700,
                mb: 0.5,
                fontSize: { xs: '1.25rem', sm: '1.5rem' },
                lineHeight: 1.2
              }}
            >
              Ampersand Capital
            </Typography>
          </Box>

          <Typography
            variant="body2"
            sx={{
              opacity: 0.9,
              mb: 2,
              fontSize: { xs: '0.875rem', sm: '1rem' }
            }}
          >
            Professional Investment Management
          </Typography>
        </Box>

        {/* Mobile Login Form */}
        <Box
          sx={{
            backgroundColor: 'white',
            borderTopLeftRadius: { xs: 24, sm: 32 },
            borderTopRightRadius: { xs: 24, sm: 32 },
            padding: { xs: 3, sm: 4 },
            paddingTop: { xs: 4, sm: 5 },
            flex: 1,
            width: '100%',
            zIndex: 1,
            marginTop: 'auto',
            maxWidth: '100%',
            overflow: 'hidden'
          }}
        >
          <Typography
            variant="h6"
            component="h1"
            sx={{
              fontWeight: 700,
              color: '#0f172a',
              mb: 0.5,
              textAlign: 'center',
              fontSize: { xs: '1.125rem', sm: '1.25rem' }
            }}
          >
            Welcome Back
          </Typography>

          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              mb: { xs: 3, sm: 4 },
              textAlign: 'center',
              fontSize: { xs: '0.875rem', sm: '1rem' }
            }}
          >
            Please sign in to access your account
          </Typography>

          <Box component="form" onSubmit={handleSubmit} sx={{ width: '100%' }}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="username-mobile"
              label="Username"
              name="username"
              autoComplete="username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              sx={{
                mb: { xs: 1.5, sm: 2 },
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  height: { xs: 44, sm: 48 },
                  backgroundColor: '#f8fafc',
                  fontSize: { xs: '0.875rem', sm: '1rem' },
                  '& fieldset': {
                    borderColor: '#e2e8f0',
                  },
                  '&:hover fieldset': {
                    borderColor: '#3b82f6',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#3b82f6',
                  },
                },
                '& .MuiInputLabel-root': {
                  fontSize: { xs: '0.875rem', sm: '1rem' },
                  '&.Mui-focused': {
                    color: '#3b82f6',
                  },
                },
              }}
              slotProps={{
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <Person sx={{ color: '#64748b', fontSize: { xs: 18, sm: 20 } }} />
                    </InputAdornment>
                  ),
                }
              }}
            />

            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="Password"
              type={showPassword ? 'text' : 'password'}
              id="password-mobile"
              autoComplete="current-password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              sx={{
                mb: { xs: 2, sm: 3 },
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  height: { xs: 44, sm: 48 },
                  backgroundColor: '#f8fafc',
                  fontSize: { xs: '0.875rem', sm: '1rem' },
                  '& fieldset': {
                    borderColor: '#e2e8f0',
                  },
                  '&:hover fieldset': {
                    borderColor: '#3b82f6',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#3b82f6',
                  },
                },
                '& .MuiInputLabel-root': {
                  fontSize: { xs: '0.875rem', sm: '1rem' },
                  '&.Mui-focused': {
                    color: '#3b82f6',
                  },
                },
              }}
              slotProps={{
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <Lock sx={{ color: '#64748b', fontSize: { xs: 18, sm: 20 } }} />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={() => setShowPassword(!showPassword)}
                        edge="end"
                        sx={{ color: '#64748b', padding: { xs: 0.5, sm: 1 } }}
                      >
                        {showPassword ? <VisibilityOff sx={{ fontSize: { xs: 18, sm: 20 } }} /> : <Visibility sx={{ fontSize: { xs: 18, sm: 20 } }} />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }
              }}
            />

            {error && (
              <Alert
                severity="error"
                sx={{
                  width: '100%',
                  mb: { xs: 2, sm: 3 },
                  borderRadius: 2,
                  backgroundColor: '#fef2f2',
                  border: '1px solid #fecaca',
                  color: '#dc2626',
                  fontSize: { xs: '0.875rem', sm: '1rem' }
                }}
              >
                {error}
              </Alert>
            )}

            <Button
              type="submit"
              fullWidth
              variant="contained"
              disabled={loading}
              sx={{
                height: { xs: 44, sm: 48 },
                borderRadius: 2,
                background: 'linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%)',
                fontSize: { xs: '1rem', sm: '1.1rem' },
                fontWeight: 600,
                textTransform: 'none',
                boxShadow: '0 4px 16px rgba(30, 64, 175, 0.3)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%)',
                  boxShadow: '0 6px 20px rgba(30, 64, 175, 0.4)',
                },
                '&:disabled': {
                  background: '#e2e8f0',
                  color: '#64748b',
                  boxShadow: 'none',
                },
              }}
            >
              {loading ? (
                <CircularProgress size={18} sx={{ color: 'white' }} />
              ) : (
                'Sign In'
              )}
            </Button>

            <Box sx={{ textAlign: 'center', mt: { xs: 2, sm: 3 } }}>
              <Link
                component={RouterLink}
                to="/password-reset"
                variant="body2"
                sx={{
                  color: '#64748b',
                  textDecoration: 'none',
                  fontSize: { xs: '0.875rem', sm: '1rem' },
                  '&:hover': {
                    color: '#3b82f6',
                    textDecoration: 'underline',
                  },
                }}
              >
                Forgot password?
              </Link>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default Login;
