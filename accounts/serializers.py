from rest_framework import serializers
# Import necessary libraries for password generation
import random
import string
from django.contrib.auth import get_user_model
from .models import Staff, Document, Client, User, Folio, FundNAVHistory, BenchmarkHistory
from django.db import transaction
from datetime import date
import calendar
from django.contrib.auth.tokens import PasswordResetTokenGenerator
from django.utils.http import urlsafe_base64_decode
from django.utils.encoding import smart_str


# Get the User model
User = get_user_model()

def generate_random_password(length=8):
    """
    Generates a random, secure password.
    
    The password will contain at least one uppercase letter, one lowercase letter,
    one digit, and one special character.
    """
    # Define character sets
    lower = string.ascii_lowercase
    upper = string.ascii_uppercase
    digits = string.digits
    special = string.punctuation

    # Ensure the password has at least one character from each set
    password = [
        random.choice(lower),
        random.choice(upper),
        random.choice(digits),
        random.choice(special)
    ]

    # Fill the rest of the password length with a mix of all characters
    all_chars = lower + upper + digits + special
    password += random.choices(all_chars, k=length - 4)

    # Shuffle the list to randomize the position of each character
    random.shuffle(password)
    
    return "".join(password)

class StaffSerializer(serializers.ModelSerializer):
   email = serializers.EmailField(write_only=True)
    #password = serializers.CharField(read_only=True ,style={'input_type': 'password'})
   first_name = serializers.CharField(write_only=True)
   last_name = serializers.CharField(write_only=True)


   class Meta:
       model = Staff
       fields = ['id', 'user', 'email', 'first_name', 'last_name']
       read_only_fields = ['id', 'user']

   def validate_email(self, value):
       if User.objects.filter(email__iexact=value).exists():
           raise serializers.ValidationError("A user with this email already exists.")
       return value


   def create(self, validated_data):
       # Remove the password from validated_data to use our generated one
       email = validated_data.pop('email')
       first_name = validated_data.pop('first_name')
       last_name = validated_data.pop('last_name')
       
       # Generate a random, secure password
       random_password = generate_random_password()
       print(random_password)  # For debugging purposes; remove in production
       
       # --- Generate a unique username ---
       base_username = f"{first_name.lower()}{last_name.lower()}"
       username = base_username
       counter = 1
       while User.objects.filter(username=username).exists():
           username = f"{base_username}_{counter}"
           counter += 1

       # Create the User object with the generated password
       user = User.objects.create_user(
           username=username,
           email=email,
           password=random_password,
           first_name=first_name,
           last_name=last_name,
           role='staff'
       )
       
       # Create the Staff object
       staff = Staff.objects.create(user=user)
       return staff

# --- Serializer for Staff List View ---
class StaffListSerializer(serializers.ModelSerializer):
    email = serializers.EmailField(source='user.email', read_only=True)
    first_name = serializers.CharField(source='user.first_name', read_only=True)
    last_name = serializers.CharField(source='user.last_name', read_only=True)

    class Meta:
        model = Staff
        fields = ['id', 'user', 'email', 'first_name', 'last_name']
        read_only_fields = ['id', 'user', 'email', 'first_name', 'last_name']

class ClientSerializer(serializers.ModelSerializer):
    email = serializers.EmailField(
        write_only=True,
        error_messages={'required': 'The Email field cannot be blank.'}
    )
    first_name = serializers.CharField(
        write_only=True,
        error_messages={'required': 'The First Name field cannot be blank.'}
    )
    last_name = serializers.CharField(
        write_only=True,
        error_messages={'required': 'The Last Name field cannot be blank.'}
    )
    pan = serializers.CharField(
        error_messages={'required': 'The PAN Number field cannot be blank.'}
    )
    folio_number = serializers.IntegerField(
        write_only=True,
        error_messages={'required': 'The Folio Number field cannot be blank.'}
    )

    class Meta:
        model = Client
        fields = ['id', 'user', 'pan', 'email', 'first_name', 'last_name', 'folio_number']
        read_only_fields = ['id', 'user']

    def validate(self, data):
        """
        Securely validates against the server-side session, not frontend flags.
        """
        request = self.context.get('request')
        if not request:
            raise serializers.ValidationError("Request context not found.")

        submitted_pan = data.get('pan', '').strip().upper()
        submitted_email = data.get('email', '').strip()
        submitted_folio_number = data.get('folio_number')

        if len(submitted_pan) > 10:
            raise serializers.ValidationError({"pan": "PAN number cannot be more than 10 characters."})

        if User.objects.filter(email__iexact=submitted_email).exists():
            raise serializers.ValidationError({"email": "This email already exists."})
        if Client.objects.filter(pan__iexact=submitted_pan).exists():
            raise serializers.ValidationError({"pan": "This PAN number already exists."})

        # Validate folio number uniqueness
        if submitted_folio_number is not None:
            if Folio.objects.filter(folio_number=submitted_folio_number).exists():
                raise serializers.ValidationError({"folio_number": "This folio number already exists."})
            if submitted_folio_number <= 0:
                raise serializers.ValidationError({"folio_number": "Folio number must be a positive integer."})

        return data

    def create(self, validated_data):
        first_name = validated_data['first_name'].lower() if validated_data['first_name'] else ''
        last_name = validated_data['last_name'].lower() if validated_data['last_name'] else ''

        # --- Generate a unique username ---
        base_username = f"{first_name}{last_name}"
        username = base_username
        counter = 1
        while User.objects.filter(username=username).exists():
            username = f"{base_username}_{counter}"
            counter += 1

        random_password = generate_random_password()
        print(random_password)

        user = User.objects.create_user(
            username=username, # Auto-generated username
            email=validated_data['email'],
            password=random_password,
            first_name=validated_data['first_name'],
            last_name=validated_data['last_name'],
            role='client'
        )

        client = Client.objects.create(
            user=user,
            pan=validated_data['pan']
        )

        # Create a Folio for the new client with the specified folio number
        folio_number = validated_data['folio_number']
        Folio.objects.create(client=client, folio_number=folio_number, terminated=False)

        # --- IMPORTANT CLEANUP ---
        # After successful registration, clear the session variables to prevent reuse.
        request = self.context.get('request')
        request.session.pop('verified_email', None)

        return client
# --- Serializer for Login ---
class LoginSerializer(serializers.Serializer):
   username = serializers.CharField()
   password = serializers.CharField(style={'input_type': 'password'}, write_only=True)

class FolioSerializer(serializers.ModelSerializer):
   client_username = serializers.CharField(source='client.user.username', read_only=True)

   class Meta:
       model = Folio
       fields = ['folio_number', 'client', 'client_username', 'created_at', 'terminated']
       read_only_fields = ['client', 'client_username', 'created_at']

# --- Serializer for Client List View ---
class ClientListSerializer(serializers.ModelSerializer):
    email = serializers.EmailField(source='user.email', read_only=True)
    first_name = serializers.CharField(source='user.first_name', read_only=True)
    last_name = serializers.CharField(source='user.last_name', read_only=True)

    class Meta:
        model = Client
        fields = ['id', 'user', 'email', 'first_name', 'last_name', 'pan']
        read_only_fields = ['id', 'user', 'email', 'first_name', 'last_name', 'pan']

# --- Serializer for Client Search Results ---
class ClientSearchSerializer(serializers.Serializer):
    username = serializers.CharField()
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    email = serializers.EmailField()
    folio_numbers = serializers.DictField(
        child=serializers.BooleanField()
    )

# --- Serializer for Staff Search Results ---
class StaffSearchSerializer(serializers.Serializer):
    username = serializers.CharField()
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    email = serializers.EmailField()

class DocumentUploadSerializer(serializers.ModelSerializer):
    """
    Handles the upload of documents and conditionally updates related NAV 
    and Benchmark models within a single database transaction.
    """
    # --- Fields for incoming data (not part of the Document model) ---
    # These are marked as write_only so they are used for input but not output
    folio_number = serializers.IntegerField(write_only=True, required=False)
    pre_tax_nav = serializers.DecimalField(max_digits=10, decimal_places=4, write_only=True, required=False)
    post_tax_nav = serializers.DecimalField(max_digits=10, decimal_places=4, write_only=True, required=False)
    fund_nav = serializers.DecimalField(max_digits=14, decimal_places=4, write_only=True, required=False)
    benchmark_value = serializers.DecimalField(max_digits=14, decimal_places=4, write_only=True, required=False)
    
    class Meta:
        model = Document
        # Add all fields required for document creation
        fields = [
            'uploaded_file', 'document_type', 'subtype', 'year', 'month',
            'folio_number', 'pre_tax_nav', 'post_tax_nav', 'fund_nav', 'benchmark_value'
        ]
        # Make model fields optional in serializer to handle them in custom validation
        extra_kwargs = {
            'subtype': {'required': False},
            'year': {'required': False},
            'month': {'required': False},
        }

    def validate_uploaded_file(self, value):
        """
        Validate that the uploaded file is a PDF and within size limits.
        """
        if not value:
            raise serializers.ValidationError("No file was uploaded.")

        # Check file extension
        file_name = value.name.lower()
        if not file_name.endswith('.pdf'):
            raise serializers.ValidationError("Only PDF files are allowed.")

        # Check file size (2MB = 2 * 1024 * 1024 bytes)
        max_size = 2 * 1024 * 1024  # 2MB in bytes
        if value.size > max_size:
            raise serializers.ValidationError(f"File size must be less than 2MB. Current file size is {value.size / (1024 * 1024):.2f}MB.")

        return value

    def validate(self, data):
        """
        Custom validation to enforce conditional field requirements.
        """
        doc_type = data.get('document_type')
        subtype = data.get('subtype')

        # --- Validation for Client Documents ---
        if doc_type == 'Client':
            if not data.get('folio_number'):
                raise serializers.ValidationError({"folio_number": "Folio Number is required for Client documents."})
            if not data.get('pre_tax_nav'):
                raise serializers.ValidationError({"pre_tax_nav": "Pre-Tax NAV is required for Client documents."})
            if not data.get('post_tax_nav'):
                raise serializers.ValidationError({"post_tax_nav": "Post-Tax NAV is required for Client documents."})

        # --- Validation for Company Fund Performance Documents ---
        elif doc_type == 'Company' and subtype == 'monthly_fund_performance':
            if not data.get('fund_nav'):
                raise serializers.ValidationError({"fund_nav": "This field is required for fund performance documents."})
            if not data.get('benchmark_value'):
                raise serializers.ValidationError({"benchmark_value": "This field is required for fund performance documents."})

        return data


    def create(self, validated_data):
        """
        Creates the Document and updates related models in an atomic transaction.
        """
        # Pop the extra data that is not part of the Document model itself
        folio_number = validated_data.pop('folio_number', None)
        pre_tax_nav = validated_data.pop('pre_tax_nav', None)
        post_tax_nav = validated_data.pop('post_tax_nav', None)
        fund_nav = validated_data.pop('fund_nav', None)
        benchmark_value = validated_data.pop('benchmark_value', None)

        doc_type = validated_data.get('document_type')
        subtype = validated_data.get('subtype')
        year = validated_data.get('year') or date.today().year
        month = validated_data.get('month') or date.today().month   

        
        # Use a transaction to ensure all or no database operations are completed
        with transaction.atomic():
            # --- Handle Client Document Logic ---
            if doc_type == 'Client':
                try:
                    folio = Folio.objects.get(folio_number=folio_number)
                    if folio.terminated:
                        raise serializers.ValidationError({"folio_number": f"Folio {folio_number} is terminated and cannot have documents uploaded."})
                    validated_data['folio'] = folio # Assign folio instance to the document
                except Folio.DoesNotExist:
                    raise serializers.ValidationError({"folio_number": f"Folio with number {folio_number} not found."})
                
                # Create the document instance first
                document, created = Document.objects.update_or_create(
                    folio=folio,
                    document_type=doc_type,
                    year=year,
                    month=month,
                    defaults={
                        'uploaded_file': validated_data['uploaded_file'],
                        'last_updated_at': date.today()
                    }
                )

                # Now, update the Folio's nav_data JSON field
                # We'll use the last day of the month for consistency
                _, last_day = calendar.monthrange(year, month)
                nav_date_key = date(year, month, last_day).strftime('%Y-%m-%d')
                
                if folio.nav_data is None:
                    folio.nav_data = {}
                
                folio.nav_data[nav_date_key] = {
                    'pre_tax_nav': str(pre_tax_nav),
                    'post_tax_nav': str(post_tax_nav)
                }
                folio.save()

            # --- Handle Company Fund Performance Logic ---
            elif doc_type == 'Company' and subtype == 'monthly_fund_performance':
                # Create the document instance
                document, created = Document.objects.update_or_create(
                    document_type=doc_type,
                    subtype=subtype,
                    year=year,
                    month=month,
                    defaults={
                        'uploaded_file': validated_data['uploaded_file'],
                        'last_updated_at': date.today()
                    }
                )

                # Update or create the history records for that month
                _, last_day = calendar.monthrange(year, month)
                record_date = date(year, month, last_day)

                FundNAVHistory.objects.update_or_create(
                    date=record_date,
                    defaults={'nav': fund_nav}
                )
                BenchmarkHistory.objects.update_or_create(
                    date=record_date,
                    defaults={'value': benchmark_value}
                )
            
            # --- Handle other document types ---
            else:
                document = Document.objects.create(**validated_data)

        return document
    
# --- Serializers for Client Dashboard ---

class DashboardDocumentSerializer(serializers.ModelSerializer):
    """
    Serializes document information for the dashboard view.
    """
    uploaded_file_url = serializers.SerializerMethodField()
    folio_number = serializers.IntegerField(source='folio.folio_number', read_only=True)
    subtype_display = serializers.SerializerMethodField()
    file_name = serializers.SerializerMethodField()

    class Meta:
        model = Document
        fields = [
            'id', 'document_type', 'subtype', 'subtype_display', 'year', 'month',
            'uploaded_file_url', 'created_at', 'folio_number', 'file_name'
        ]
    def get_subtype_display(self, obj):
        return obj.get_subtype_display() if obj.subtype else None

    def get_uploaded_file_url(self, obj):
        if obj.uploaded_file:
            # For S3/LocalStack storage, the URL is already absolute
            # Don't use build_absolute_uri as it can cause malformed URLs
            return obj.uploaded_file.url
        return None

    def get_file_name(self, obj):
        """Extract filename from the uploaded file path."""
        if obj.uploaded_file:
            return obj.uploaded_file.name.split('/')[-1]
        return None
    

class DashboardFolioSerializer(serializers.ModelSerializer):
    """
    Serializes folio data, including its NAV history and associated documents.
    """
    documents = DashboardDocumentSerializer(many=True, read_only=True)
    client_username = serializers.CharField(source='client.user.username', read_only=True)

    class Meta:
        model = Folio
        fields = ['folio_number', 'client_username', 'created_at', 'terminated', 'nav_data', 'documents']

class ColumnChartDataSerializer(serializers.Serializer):
    
    """
    Serializes the data required for the performance column chart.
    """
    period = serializers.CharField()
    fund_performance = serializers.FloatField()
    benchmark_performance = serializers.FloatField()


# --- Serializers for Password Reset ---
class PasswordResetRequestSerializer(serializers.Serializer):
    """
    Serializer for requesting a password reset e-mail.
    """
    email = serializers.EmailField(required=True)

class SetNewPasswordSerializer(serializers.Serializer):
    """
    Serializer for confirming a password reset and setting a new password.
    """
    password = serializers.CharField(min_length=6, write_only=True, required=True)
    token = serializers.CharField(write_only=True, required=True)
    uidb64 = serializers.CharField(write_only=True, required=True)

    def validate(self, attrs):
        try:
            password = attrs.get('password')
            token = attrs.get('token')
            uidb64 = attrs.get('uidb64')

            user_id = smart_str(urlsafe_base64_decode(uidb64))
            user = User.objects.get(id=user_id)

            # Check if the token is valid for the user
            if not PasswordResetTokenGenerator().check_token(user, token):
                raise serializers.ValidationError('The reset link is invalid or has expired.', code='authorization')

            # Set the new password
            user.set_password(password)
            user.save()

            return user
        except (ValueError, User.DoesNotExist, TypeError, OverflowError):
            raise serializers.ValidationError('The reset link is invalid or has expired.', code='authorization')

# --- Serializers for Staff Document Manager ---

class StaffDocumentSerializer(serializers.ModelSerializer):
    """
    Serializes document information for staff document manager.
    """
    file_name = serializers.SerializerMethodField()
    file_size = serializers.SerializerMethodField()
    client_name = serializers.SerializerMethodField()
    folio_number = serializers.IntegerField(source='folio.folio_number', read_only=True)
    subtype_display = serializers.SerializerMethodField()

    class Meta:
        model = Document
        fields = [
            'id', 'document_type', 'subtype', 'subtype_display', 'year', 'month',
            'file_name', 'file_size', 'created_at', 'last_updated_at',
            'folio_number', 'client_name'
        ]
    def get_subtype_display(self, obj):
        return obj.get_subtype_display() if obj.subtype else None

    def get_file_name(self, obj):
        """Extract filename from the uploaded file path."""
        if obj.uploaded_file:
            return obj.uploaded_file.name.split('/')[-1]
        return None

    def get_file_size(self, obj):
        """Get file size in bytes."""
        if obj.uploaded_file:
            try:
                return obj.uploaded_file.size
            except (OSError, ValueError):
                return None
        return None

    def get_client_name(self, obj):
        """Get client name for client documents."""
        if obj.document_type == 'Client' and obj.folio and obj.folio.client:
            user = obj.folio.client.user
            return f"{user.first_name} {user.last_name}".strip() or user.username
        return None

class StaffDocumentManagerSerializer(serializers.Serializer):
    """
    Serializes the complete document structure for staff document manager.
    Returns company documents and client documents organized by folio.
    """
    company_documents = serializers.SerializerMethodField()
    client_documents = serializers.SerializerMethodField()
    filters_applied = serializers.SerializerMethodField()
    total_documents = serializers.SerializerMethodField()

    def get_company_documents(self, obj):
        """Serialize company documents."""
        company_docs = obj['company_documents']
        return StaffDocumentSerializer(company_docs, many=True).data

    def get_client_documents(self, obj):
        """
        Serialize client documents organized by folio with client names.
        """
        client_docs = obj['client_documents']
        
        # Group documents by folio
        folio_groups = {}
        for doc in client_docs:
            folio_number = doc.folio.folio_number if doc.folio else 'unknown'
            if folio_number not in folio_groups:
                folio_groups[folio_number] = {
                    'folio_number': folio_number,
                    'client_name': None,
                    'documents': []
                }
                
                # Get client name for this folio
                if doc.folio and doc.folio.client:
                    user = doc.folio.client.user
                    client_name = f"{user.first_name} {user.last_name}".strip()
                    folio_groups[folio_number]['client_name'] = client_name or user.username
            
            folio_groups[folio_number]['documents'].append(doc)
        
        # Serialize documents for each folio
        result = []
        for folio_data in folio_groups.values():
            serialized_docs = StaffDocumentSerializer(folio_data['documents'], many=True).data
            result.append({
                'folio_number': folio_data['folio_number'],
                'client_name': folio_data['client_name'],
                'documents': serialized_docs
            })
        
        # Sort by folio number
        result.sort(key=lambda x: x['folio_number'] if isinstance(x['folio_number'], int) else 0)
        return result

    def get_filters_applied(self, obj):
        """Return information about applied filters."""
        filters = {}
        if obj.get('year_filter'):
            filters['year'] = obj['year_filter']
        if obj.get('month_filter'):
            filters['month'] = obj['month_filter']
        return filters

    def get_total_documents(self, obj):
        """Return total count of documents."""
        company_count = len(obj['company_documents'])
        client_count = len(obj['client_documents'])
        return {
            'total': company_count + client_count,
            'company': company_count,
            'client': client_count
        }
